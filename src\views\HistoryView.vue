<template>
  <div>
    <div class="col-lg-8 mx-auto d-flex">
      <div class="exam-card">
        <div class="text-center">
          <h1 class="display-4 gradient-text fw-bold mb-3">考試紀錄</h1>
          <p class="text-muted fs-5">查看您的考試記錄與成績</p>
        </div>

        <div class="col-md-2 mx-auto mb-4 bg-info text-white text-center rouded p-1 rounded-pill">
          <span class="fs-4 fw-bold"><i class="fa-solid fa-clipboard"></i></span>
          <span class="fs-5 fw-bold ms-2">{{ examHistory.length }} 次</span>
        </div>


        <div v-if="examHistory.length === 0" class="text-center py-5">
          <i class="fas fa-history fs-1 text-muted mb-3"></i>
          <h4 class="text-muted">尚無考試記錄</h4>
          <p class="text-muted">完成第一次考試後，記錄將會顯示在這裡</p>
          <router-link to="/language" class="btn btn-gradient">
            <i class="fas fa-play me-2"></i>
            開始考試
          </router-link>
        </div>

        <div v-else>

          <!-- 考試記錄表格 -->
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead>
                <tr>
                  <th>考試時間</th>
                  <th class="text-center">類型</th>
                  <th class="text-center">章節</th>
                  <th class="text-center">分數</th>
                  <th class="text-center">答對/總題數</th>
                  <th class="text-center">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="exam in examHistory" :key="exam.id">
                  <td>
                    <small class="text-muted">{{ exam.date }}</small>
                  </td>
                  <td class="text-center">
                    <span class="badge" :class="exam.language === 'chinese' ? 'bg-info' : 'bg-primary'">
                      {{ exam.languageText }}
                    </span>
                  </td>
                  <td class="text-center">
                    {{ exam.chapter.title }}
                  </td>
                  <td class="text-center">
                    <span class="fw-bold" :class="getScoreBadgeClass(exam.score)">
                      {{ exam.score }}
                    </span>
                  </td>
                  <td class="text-center">
                    <span class="text-success fw-bold">{{ exam.correctCount }}</span>
                    /
                    <span class="text-muted">{{ exam.totalQuestions }}</span>
                  </td>
                  <td class="text-center">
                    <button class="btn btn-sm btn-outline-primary" @click="viewExamDetail(exam)">
                      <i class="fas fa-eye me-1"></i>
                      回顧
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'

const router = useRouter()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const examHistory = computed(() => examStore.examHistory)

const getScoreBadgeClass = (score) => {
  if (score >= 90) return 'text-success'
  if (score >= 80) return 'text-primary'
  if (score >= 60) return 'text-warning'
  return 'text-danger'
}

const viewExamDetail = (exam) => {
  // 將選中的考試資料設置到 store 中，然後導航到詳情頁面
  examStore.selectedExamHistory = exam
  router.push(`/history/${exam.id}?from=history`)
}
</script>

<style scoped>
.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-header {
  border-bottom: 2px solid #e9ecef;
}

.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}


@media (max-width: 768px) {
  .badge {
    font-size: 0.7rem !important;
  }

  .btn-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}
</style>
