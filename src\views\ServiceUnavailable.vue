<template>
  <div class="col-md-6 mx-auto">
    <div class="exam-card text-center">
      <div class="icon-container">
        <i class="fas fa-clock"></i>
      </div>

      <h1 class="title">目前非開放時間</h1>

      <p class="subtitle">
        感謝您的使用，本服務已於 {{ formattedEndDate }} 結束
      </p>

      <div class="footer-text">
        <p>&copy; {{ currentYear }} 考試練習系統</p>
      </div>
    </div>
  </div>

</template>

<script>
import { computed } from 'vue'

// 服務結束日期：2025年6月1日 (與顯示的時間保持一致)
const SERVICE_END_DATE = new Date('2025-06-30T00:00:00')

// 檢查服務是否在開放時間內
export const isServiceAvailable = () => {
  const now = new Date()
  return now < SERVICE_END_DATE
}

export default {
  setup() {
    const currentYear = computed(() => new Date().getFullYear())

    // 格式化服務結束日期為中文顯示
    const formattedEndDate = computed(() => {
      const year = SERVICE_END_DATE.getFullYear()
      const month = SERVICE_END_DATE.getMonth() + 1
      const day = SERVICE_END_DATE.getDate()
      return `${year}年${month}月${day}日`
    })

    return {
      currentYear,
      formattedEndDate
    }
  }
}
</script>

<style scoped>
.icon-container {
  margin-bottom: 30px;
}

.icon-container i {
  font-size: 4rem;
  color: #667eea;
  opacity: 0.8;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 20px;
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 1.2rem;
  color: #4a5568;
  margin-bottom: 40px;
  line-height: 1.6;
  font-weight: 400;
}

.footer-text {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.footer-text p {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 40px 30px;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .icon-container i {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 30px 20px;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .icon-container i {
    font-size: 2.5rem;
  }
}

/* 動畫效果 */
.content-wrapper {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-container i {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}
</style>
