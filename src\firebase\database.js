import { ref, get, set, push } from 'firebase/database'
import { database } from './config'

// 獲取題目資料
export const getQuestions = async (language, chapter) => {
  try {
    const questionsRef = ref(database, `${language}/${chapter}`)
    const snapshot = await get(questionsRef)

    if (snapshot.exists()) {
      return { success: true, data: snapshot.val() }
    } else {
      return { success: false, error: '找不到題目資料' }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 獲取所有語言的章節列表
export const getChapters = async (language) => {
  try {
    console.log('getChapters - 載入語言:', language)
    const chaptersRef = ref(database, language)
    const snapshot = await get(chaptersRef)

    if (snapshot.exists()) {
      const data = snapshot.val()
      console.log('getChapters - 原始資料:', data)

      // 轉換為章節列表格式：第一層 key 是章節名稱
      const chapters = Object.keys(data).map((chapterName, index) => ({
        id: index + 1,
        title: chapterName, // 直接使用第一層的 key 作為標題
        desc: `${chapterName}相關題目`,
        name: chapterName, // 保存原始名稱用於後續查詢
        questionCount: data[chapterName].length,
      }))

      console.log('getChapters - 轉換後的章節:', chapters)
      return { success: true, data: chapters }
    } else {
      console.log('getChapters - 沒有找到資料')
      return { success: false, error: '找不到章節資料' }
    }
  } catch (error) {
    console.error('getChapters - 錯誤:', error)
    return { success: false, error: error.message }
  }
}

// 保存用戶答題記錄
export const saveUserAnswers = async (userId, examData) => {
  try {
    console.log('=== Firebase saveUserAnswers 開始 ===')
    console.log('用戶 ID:', userId)
    console.log('考試資料:', examData)

    const userAnswersRef = ref(database, `userAnswers/${userId}`)
    console.log('Firebase 路徑:', `userAnswers/${userId}`)

    const newAnswerRef = push(userAnswersRef)
    console.log('新記錄引用:', newAnswerRef.key)

    const answerRecord = {
      ...examData,
      timestamp: Date.now(),
      date: new Date().toLocaleString('zh-TW'),
    }

    console.log('準備寫入的完整記錄:', answerRecord)
    await set(newAnswerRef, answerRecord)
    console.log('Firebase 寫入成功，記錄 ID:', newAnswerRef.key)

    return { success: true, id: newAnswerRef.key }
  } catch (error) {
    console.error('Firebase saveUserAnswers 錯誤:', error)
    return { success: false, error: error.message }
  }
}

// 獲取用戶答題歷史
export const getUserAnswers = async (userId) => {
  try {
    const userAnswersRef = ref(database, `userAnswers/${userId}`)
    const snapshot = await get(userAnswersRef)

    if (snapshot.exists()) {
      const data = snapshot.val()
      // 轉換為陣列格式並按時間排序
      const answers = Object.keys(data)
        .map((key) => ({
          id: key,
          ...data[key],
        }))
        .sort((a, b) => b.timestamp - a.timestamp)

      return { success: true, data: answers }
    } else {
      return { success: true, data: [] }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}
