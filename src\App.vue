<script setup>
import { RouterView, useRoute } from 'vue-router'
import { ref, onMounted, computed } from 'vue'
import { useExamStore } from './stores/examStore'
import ServiceUnavailable, { isServiceAvailable } from './views/ServiceUnavailable.vue'
import TopNavigation from './components/TopNavigation.vue'

const route = useRoute()
const examStore = useExamStore()
const serviceAvailable = ref(true)

// 檢查服務是否可用
const checkServiceAvailability = () => {
  serviceAvailable.value = isServiceAvailable()
}

// 判斷是否需要顯示導覽列
const shouldShowNavigation = computed(() => {
  // 不需要導覽列的頁面
  const pagesWithoutNav = ['/', '/register']
  return !pagesWithoutNav.includes(route.path)
})

onMounted(() => {
  checkServiceAvailability()

  // 每分鐘檢查一次服務狀態
  setInterval(checkServiceAvailability, 60000)
})
</script>

<template>
  <div>
    <!-- 頂部導航欄 - 只在需要的頁面顯示 -->
    <TopNavigation v-if="shouldShowNavigation && serviceAvailable" />

    <div class="container px-2 px-sm-4 pt-5" :class="shouldShowNavigation ? 'with-navbar' : 'pt-5'">
      <!-- 如果服務不可用，顯示服務不可用頁面 -->
      <ServiceUnavailable v-if="!serviceAvailable" />

      <!-- 如果服務可用，顯示正常的路由內容 -->
      <RouterView v-else :key="$route.path + examStore.currentLanguage" />
    </div>
  </div>

</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100%;
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
}

/* 有導覽列時的頂部間距 */
.with-navbar {
  padding-top: 100px !important;
}

@media (max-width: 768px) {
  .with-navbar {
    padding-top: 90px !important;
  }
}
</style>
