<template>
  <div>
    <div class="col-lg-6 mx-auto d-flex">
      <div class="exam-card">
        <h1 class="gradient-text fw-bold text-center mb-3">選擇模式</h1>

        <!-- 測驗功能 -->
        <div class="mb-5">
          <h4 class="text-center mb-3 alert alert-primary fw-bold p-2">
            <i class="fas fa-clipboard-check me-2"></i>
            測驗
          </h4>
          <div class="row g-3 mb-4">
            <div class="col-md-6">
              <button class="language-card chinese w-100 h-100 border-0" @click="selectLanguage('中文')">
                <div class="fs-4 fw-bold text-center">✏️ 中文測驗</div>
              </button>
            </div>

            <div class="col-md-6">
              <button class="language-card english w-100 h-100 border-0" @click="selectLanguage('英文')">
                <div class="fs-4 fw-bold">✏️ 英文測驗(US)</div>
              </button>
            </div>
          </div>
        </div>

        <!-- 題庫功能 -->
        <div class="mb-4">
          <h4 class="text-center mb-3 alert alert-primary fw-bold p-2">
            <i class="fas fa-book me-2"></i>
            題庫
          </h4>
          <div class="row g-3 mb-4">
            <div class="col-md-6">
              <button class="language-card chinese chinese w-100 h-100 border-0" @click="selectQuestionBank('中文')">
                <div class="fs-4 fw-bold">📖 中文題庫</div>
              </button>
            </div>

            <div class="col-md-6">
              <button class="language-card english english w-100 h-100 border-0" @click="selectQuestionBank('英文')">
                <div class="fs-4 fw-bold">📖 英文題庫(US)</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'

const router = useRouter()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const selectLanguage = (language) => {
  examStore.selectLanguage(language)
  examStore.setMode('exam') // 設置為考試模式
  router.push('/chapters')
}

const selectQuestionBank = (language) => {
  examStore.selectLanguage(language)
  examStore.setMode('questionBank') // 設置為題庫模式
  router.push('/chapters')
}
</script>

<style scoped>
.language-card.question-bank {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.language-card.question-bank:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .language-card {
    padding: 15px;
  }
}
</style>
